import Flutter
import UIKit
import UserNotifications
import BackgroundTasks
import WidgetKit
import workmanager

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    if #available(iOS 10.0, *) {
      UNUserNotificationCenter.current().delegate = self
    }

    // Register for background tasks
    if #available(iOS 13.0, *) {
      BGTaskScheduler.shared.register(forTaskWithIdentifier: "workmanager.background.task", using: nil) { task in
        // This code will be executed when the background task is triggered
        self.handleBackgroundTask(task: task as! BGAppRefreshTask)
      }
    }

    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  @available(iOS 13.0, *)
  func handleBackgroundTask(task: BGAppRefreshTask) {
    // Schedule a new background task before this one expires
    scheduleBackgroundTask()

    // Create a task request that will execute the Flutter background code
    let flutterEngine = (UIApplication.shared.delegate as! AppDelegate).flutterEngine

    // Set up a task expiration handler
    task.expirationHandler = {
      // If the task expires, we need to complete it
      task.setTaskCompleted(success: false)
    }

    // Refresh the widget
    if #available(iOS 14.0, *) {
      WidgetCenter.shared.reloadTimelines(ofKind: "PrayerWidget")
    }

    // Mark the task as complete
    task.setTaskCompleted(success: true)
  }

  @available(iOS 13.0, *)
  func scheduleBackgroundTask() {
    let request = BGAppRefreshTaskRequest(identifier: "workmanager.background.task")
    request.earliestBeginDate = Date(timeIntervalSinceNow: 15 * 60) // 15 minutes from now

    do {
      try BGTaskScheduler.shared.submit(request)
      print("Background task scheduled successfully")
    } catch {
      print("Could not schedule background task: \(error)")
    }
  }
}

//import Flutter
//import UIKit
//import WidgetKit
//
//@main
//@objc class AppDelegate: FlutterAppDelegate {
//  override func application(
//    _ application: UIApplication,
//    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
//  ) -> Bool {
//    GeneratedPluginRegistrant.register(with: self)
//    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
//  }
//
//  // Add a method to reload widget timelines
//  @objc func reloadPrayerWidget() {
//    if #available(iOS 14.0, *) {
//      WidgetCenter.shared.reloadTimelines(ofKind: "PrayerWidget")
//    } else {
//      print("WidgetCenter is not available on iOS versions below 14.0")
//    }
//  }
//}

//import Flutter
//import UIKit
//
//@main
//@objc class AppDelegate: FlutterAppDelegate {
//  override func application(
//    _ application: UIApplication,
//    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
//  ) -> Bool {
//    GeneratedPluginRegistrant.register(with: self)
//    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
//  }
//}

//import Flutter
//import UIKit
//
//@main
//@objc class AppDelegate: FlutterAppDelegate {
//  override func application(
//    _ application: UIApplication,
//    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
//  ) -> Bool {
//    GeneratedPluginRegistrant.register(with: self)
//    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
//  }
//}
