import Flutter
import UIKit
import UserNotifications
import WidgetKit

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    // Set the notification center delegate
    UNUserNotificationCenter.current().delegate = self

    // Register generated plugins
    GeneratedPluginRegistrant.register(with: self)

    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }

  // Method to reload widget timelines
  @objc func reloadPrayerWidget() {
    WidgetCenter.shared.reloadTimelines(ofKind: "PrayerWidget")
    print("Prayer widget timeline reloaded")
  }
}


