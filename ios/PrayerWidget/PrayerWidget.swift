import WidgetKit
import SwiftUI

struct SimpleEntry: TimelineEntry {
    let date: Date
    let titleText: String
    let nextPrayerName: String
    let nextPrayerTime: String
    let fajr: String
    let dhuhr: String
    let asr: String
    let maghrib: String
    let isha: String
}

struct Provider: TimelineProvider {
    func placeholder(in context: Context) -> SimpleEntry {
        SimpleEntry(
            date: Date(),
            titleText: "Title Text Data",
            nextPrayerName: "--",
            nextPrayerTime: "--",
            fajr: "--", dhuhr: "--", asr: "--", maghrib: "--", isha: "--"
        )
    }

    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> Void) {
        completion(createEntry(forPrayer: "الفجر", at: Date()))
    }

    func getTimeline(in context: Context, completion: @escaping (Timeline<SimpleEntry>) -> Void) {
        let baseTime = Calendar.current.startOfDay(for: Date())
            .addingTimeInterval(8 * 3600) // Start from 8:00 AM

        let prayerTimes = [
            ("الفجر", baseTime),
            ("الظهر", baseTime.addingTimeInterval(5 * 60)),
            ("العصر", baseTime.addingTimeInterval(10 * 60)),
            ("المغرب", baseTime.addingTimeInterval(15 * 60)),
            ("العشاء", baseTime.addingTimeInterval(20 * 60))
        ]

        let prayerMap: [String: String] = [
            "الفجر": "08:00 ص",
            "الظهر": "08:05 ص",
            "العصر": "08:10 ص",
            "المغرب": "08:15 ص",
            "العشاء": "08:20 ص"
        ]

        var entries: [SimpleEntry] = []

        for (name, date) in prayerTimes {
            let entry = SimpleEntry(
                date: date,
                titleText: "موعد الصلاة",
                nextPrayerName: name,
                nextPrayerTime: prayerMap[name] ?? "--",
                fajr: prayerMap["الفجر"] ?? "--",
                dhuhr: prayerMap["الظهر"] ?? "--",
                asr: prayerMap["العصر"] ?? "--",
                maghrib: prayerMap["المغرب"] ?? "--",
                isha: prayerMap["العشاء"] ?? "--"
            )
            entries.append(entry)
        }

        completion(Timeline(entries: entries, policy: .atEnd))
    }

    private func createEntry(forPrayer name: String, at date: Date) -> SimpleEntry {
        let staticTimes: [String: String] = [
            "الفجر": "08:00 ص",
            "الظهر": "08:05 ص",
            "العصر": "08:10 ص",
            "المغرب": "08:15 ص",
            "العشاء": "08:20 ص"
        ]

        return SimpleEntry(
            date: date,
            titleText: "موعد الصلاة",
            nextPrayerName: name,
            nextPrayerTime: staticTimes[name] ?? "--",
            fajr: staticTimes["الفجر"]!,
            dhuhr: staticTimes["الظهر"]!,
            asr: staticTimes["العصر"]!,
            maghrib: staticTimes["المغرب"]!,
            isha: staticTimes["العشاء"]!
        )
    }
}

//struct Provider: TimelineProvider {
//    func placeholder(in context: Context) -> SimpleEntry {
//        SimpleEntry(
//            date: Date(),
//            titleText: "Title Text Data",
//            nextPrayerName: "--",
//            nextPrayerTime: "--",
//            fajr: "--", dhuhr: "--", asr: "--", maghrib: "--", isha: "--"
//        )
//    }
//
//    func getSnapshot(in context: Context, completion: @escaping (SimpleEntry) -> Void) {
//        completion(readEntry())
//    }
//
//    func getTimeline(in context: Context, completion: @escaping (Timeline<SimpleEntry>) -> Void) {
//        var entries: [SimpleEntry] = []
//        let currentDate = Date()
//
//        for hourOffset in 0..<5 {
//            let entryDate = Calendar.current.date(byAdding: .hour, value: hourOffset, to: currentDate)!
//            let entry = readEntry(date: entryDate)
//            entries.append(entry)
//        }
//
//        completion(Timeline(entries: entries, policy: .atEnd))
//    }
//
//    private func readEntry(date: Date = Date()) -> SimpleEntry {
//        let userDefaults = UserDefaults(suiteName: "group.qurankareem")
//        let nextPrayerValue = userDefaults?.string(forKey: "nextPrayer") ?? "--\n--"
//        let nextPrayerComponents = nextPrayerValue.components(separatedBy: "\n")
//        let nextPrayerName = nextPrayerComponents.first ?? "--"
//        let nextPrayerTime = nextPrayerComponents.last ?? "--"
//
//        return SimpleEntry(
//            date: date,
//            titleText: userDefaults?.string(forKey: "titleText") ?? "No Title",
//            nextPrayerName: nextPrayerName,
//            nextPrayerTime: nextPrayerTime,
//            fajr: userDefaults?.string(forKey: "fajr") ?? "--",
//            dhuhr: userDefaults?.string(forKey: "dhuhr") ?? "--",
//            asr: userDefaults?.string(forKey: "asr") ?? "--",
//            maghrib: userDefaults?.string(forKey: "maghrib") ?? "--",
//            isha: userDefaults?.string(forKey: "isha") ?? "--"
//        )
//    }
//}

struct PrayerWidgetEntryView: View {
    var entry: SimpleEntry

    var body: some View {
        VStack(alignment: .leading, spacing: 10) {
            HStack {
                VStack(alignment: .trailing) {
                    Text(entry.nextPrayerName)
                        .font(.title2)
                        .foregroundColor(Color(red: 1.0, green: 0.75, blue: 0.0))
                        .bold()
                    Text(entry.nextPrayerTime)
                        .font(.title2)
                        .foregroundColor(Color(red: 1.0, green: 0.75, blue: 0.0))
                }

                Spacer()

                Text(entry.titleText)
                    .font(.title)
                    .bold()
                    .foregroundColor(.white)
            }

            Divider()
                .background(Color.white)

            HStack {
                prayerTimeView(label: "العشاء", time: entry.isha, highlight: entry.nextPrayerName == "العشاء")
                prayerTimeView(label: "المغرب", time: entry.maghrib, highlight: entry.nextPrayerName == "المغرب")
                prayerTimeView(label: "العصر", time: entry.asr, highlight: entry.nextPrayerName == "العصر")
                prayerTimeView(label: "الظهر", time: entry.dhuhr, highlight: entry.nextPrayerName == "الظهر")
                prayerTimeView(label: "الفجر", time: entry.fajr, highlight: entry.nextPrayerName == "الفجر")
            }
            .font(.caption)
            .frame(maxWidth: .infinity)
            .padding(.top, 4)
        }
        .padding()
        .background(
            Image("prayer_bg")
             .resizable()
          .aspectRatio(contentMode: .fill)
         .frame(width: UIScreen.main.bounds.width, height: .infinity)
        )
    }

    private func prayerTimeView(label: String, time: String, highlight: Bool) -> some View {
        VStack {
            Text(label)
                .fontWeight(highlight ? .bold : .regular)
                .foregroundColor(highlight ? Color(red: 1.0, green: 0.75, blue: 0.0) : .white)
            Text(time)
                .fontWeight(highlight ? .bold : .regular)
                .foregroundColor(highlight ? Color(red: 1.0, green: 0.75, blue: 0.0) : .white)
        }
        .frame(maxWidth: .infinity)
    }
}


struct PrayerWidget: Widget {
    let kind: String = "PrayerWidget"

    var body: some WidgetConfiguration {
        StaticConfiguration(kind: kind, provider: Provider()) { entry in
            PrayerWidgetEntryView(entry: entry)
                .containerBackground(.clear, for: .widget)
        }
        .configurationDisplayName("Prayer Widget")
        .description("View Prayer Times")
        .supportedFamilies([.systemMedium])
    }
}


#Preview(as: .systemMedium) {
    PrayerWidget()
} timeline: {
    SimpleEntry(
        date: .now,
        titleText: "موعد الصلاة",
        nextPrayerName: "الفجر",
        nextPrayerTime: "4:20 ص",
        fajr: "4:20 ص",
        dhuhr: "12:15 م",
        asr: "3:45 م",
        maghrib: "6:20 م",
        isha: "7:50 م"
    )
}

