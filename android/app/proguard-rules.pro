# Keep all classes in the com.perfectfit.qurankareem package and its subpackages
-keep class com.perfectfit.qurankareem.** { *; }

# Do not warn about missing classes in the com.perfectfit.qurankareem.lib package and its subpackages
-dontwarn com.perfectfit.qurankareem.lib.**

# Keep all Flutter classes
-keep class io.flutter.app.** { *; }
-keep class io.flutter.embedding.** { *; }
-keep class io.flutter.plugin.** { *; }

# Keep all classes in the Dart package
-keep class io.flutter.plugins.** { *; }

# Keep all classes in the Kotlin package
-keep class kotlin.** { *; }

# Keep all classes in the AndroidX package
-keep class androidx.** { *; }

# Keep all classes in the Firebase package
-keep class com.google.firebase.** { *; }

# Keep all classes in the WorkManager package
-keep class androidx.work.** { *; }

# Keep all classes in the Awesome Notifications package
-keep class me.carda.awesome_notifications.** { *; }

# Keep all classes in the AudioService package
-keep class com.ryanheise.audioservice.** { *; }

# Keep all classes in the HomeWidget package
-keep class com.perfectfit.qurankareem.** { *; }

# Keep all classes in the CircularCountDownTimer package
-keep class com.perfectfit.qurankareem.CircularCountDownTimer.** { *; }

# Keep all classes in the HomeBanner package
-keep class com.perfectfit.qurankareem.HomeBanner.** { *; }

# Keep all classes in the MainScreen package
-keep class com.perfectfit.qurankareem.MainScreen.** { *; }

# Keep Google Play Core classes
-keep class com.google.android.play.core.** { *; }
-dontwarn com.google.android.play.core.**