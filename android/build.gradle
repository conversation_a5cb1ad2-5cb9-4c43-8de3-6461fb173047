allprojects {
    repositories {
        google()
        mavenCentral()
        // [required] background_fetch
        maven { url "${project(':background_fetch').projectDir}/libs" }
    }
}

rootProject.buildDir = "../build"
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"

    afterEvaluate {
        android {
            compileSdkVersion 34
        }
    }
}
subprojects {
    project.evaluationDependsOn(":app")
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
