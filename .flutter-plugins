# This is a generated file; do not edit or check into version control.
audio_service=/Users/<USER>/.pub-cache/hosted/pub.dev/audio_service-0.18.16/
audio_service_web=/Users/<USER>/.pub-cache/hosted/pub.dev/audio_service_web-0.1.3/
audio_session=/Users/<USER>/.pub-cache/hosted/pub.dev/audio_session-0.1.25/
awesome_notifications=/Users/<USER>/.pub-cache/hosted/pub.dev/awesome_notifications-0.10.1/
background_fetch=/Users/<USER>/.pub-cache/hosted/pub.dev/background_fetch-1.3.7/
connectivity_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/connectivity_plus-6.1.3/
eraser=/Users/<USER>/.pub-cache/hosted/pub.dev/eraser-3.0.0/
firebase_core=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core-3.11.0/
firebase_core_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_core_web-2.20.0/
firebase_messaging=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging-15.2.2/
firebase_messaging_web=/Users/<USER>/.pub-cache/hosted/pub.dev/firebase_messaging_web-3.10.2/
flutter_compass_v2=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_compass_v2-1.0.3/
flutter_exit_app=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_exit_app-1.1.4/
flutter_local_notifications=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications-19.2.1/
flutter_local_notifications_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_linux-6.0.0/
flutter_local_notifications_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_local_notifications_windows-1.0.0/
flutter_native_splash=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_native_splash-2.4.4/
flutter_qiblah=/Users/<USER>/.pub-cache/hosted/pub.dev/flutter_qiblah-3.1.0+1/
fluttertoast=/Users/<USER>/.pub-cache/hosted/pub.dev/fluttertoast-8.2.10/
geolocator=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator-13.0.2/
geolocator_android=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_android-4.6.1/
geolocator_apple=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_apple-2.3.8+1/
geolocator_web=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_web-4.1.1/
geolocator_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/geolocator_windows-0.2.3/
home_widget=/Users/<USER>/.pub-cache/hosted/pub.dev/home_widget-0.7.0+1/
just_audio=/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio-0.9.46/
just_audio_web=/Users/<USER>/.pub-cache/hosted/pub.dev/just_audio_web-0.4.14/
location=/Users/<USER>/.pub-cache/hosted/pub.dev/location-8.0.0/
location_web=/Users/<USER>/.pub-cache/hosted/pub.dev/location_web-6.0.0/
package_info_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/package_info_plus-8.1.2/
path_provider=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider-2.1.5/
path_provider_android=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_android-2.2.15/
path_provider_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_foundation-2.4.1/
path_provider_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_linux-2.2.1/
path_provider_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/path_provider_windows-2.3.0/
permission_handler=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler-11.3.1/
permission_handler_android=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_android-12.0.13/
permission_handler_apple=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_apple-9.4.5/
permission_handler_html=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_html-0.1.3+5/
permission_handler_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/permission_handler_windows-0.2.1/
share_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/share_plus-10.1.4/
shared_preferences=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences-2.4.0/
shared_preferences_android=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_android-2.4.4/
shared_preferences_foundation=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_foundation-2.5.4/
shared_preferences_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_linux-2.4.1/
shared_preferences_web=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_web-2.4.2/
shared_preferences_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/shared_preferences_windows-2.4.1/
sqflite=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite-2.4.1/
sqflite_android=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_android-2.4.0/
sqflite_darwin=/Users/<USER>/.pub-cache/hosted/pub.dev/sqflite_darwin-2.4.1/
url_launcher=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher-6.3.1/
url_launcher_android=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_android-6.3.14/
url_launcher_ios=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_ios-6.3.2/
url_launcher_linux=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_linux-3.2.1/
url_launcher_macos=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_macos-3.2.2/
url_launcher_web=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_web-2.3.3/
url_launcher_windows=/Users/<USER>/.pub-cache/hosted/pub.dev/url_launcher_windows-3.1.3/
wakelock_plus=/Users/<USER>/.pub-cache/hosted/pub.dev/wakelock_plus-1.2.10/
workmanager=/Users/<USER>/.pub-cache/hosted/pub.dev/workmanager-0.5.2/
