import 'dart:io';

import 'package:background_fetch/background_fetch.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:home_widget/home_widget.dart';
import 'package:quran_broadcast_app/src/core/consts/app_constants.dart';
import 'package:quran_broadcast_app/src/core/shared/services/home_widgets_service/home_widget.service.dart';
import 'package:quran_broadcast_app/src/features/calendar/controllers/calendar.controller.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../features/home/<USER>/widgets/next_prayer_times.dart';

@pragma('vm:entry-point')
void backgroundFetchHeadlessTask(taskId) async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorageService.init();
  await HomeWidget.setAppGroupId(AppConsts.homeWidgetAppGroupId);

  final calendarController =
      ProviderContainer().read(calendarControllerNotifierProvider);

  CalendarController.calendar.value =
      await calendarController.getCalendarFromLocal();

  final now = DateTime.now();
  var currentDayData = calendarController.calendarByDate(now);
  var prayerTimes = currentDayData.prayerTimes;
  var nextPrayerTime = getNextPrayerTime(prayerTimes, date: now);

  if (nextPrayerTime.name == AppConsts.prayerNames[0]) {
    final nextDay = (now.hour >= 17 && now.hour <= 23)
        ? DateTime(now.year, now.month, now.day + 1, 0, 0)
        : now;

    Log.w('Fetching next day data for Fajr: $nextDay');

    currentDayData = calendarController.calendarByDate(nextDay);

    prayerTimes = currentDayData.prayerTimes;

    nextPrayerTime = getNextPrayerTime(prayerTimes, date: nextDay);

    Log.i(
        'Next Fajr time: ${nextPrayerTime.name}, ${nextPrayerTime.time}, Date: $nextDay');
  }

  currentDayData = calendarController.calendarByDate(now);
  prayerTimes = currentDayData.prayerTimes;

  Log.w(
      'Next prayer time: ${nextPrayerTime.name}, ${nextPrayerTime.time}, Date: $now\nDone');

  await HomeWidgetService.savePrayerTimes(prayerTimes, nextPrayerTime);
  await HomeWidget.updateWidget(
      iOSName: AppConsts.iosWidget, androidName: AppConsts.androidWidget);

  if (Platform.isAndroid) {
    await HomeWidget.updateWidget(
        iOSName: AppConsts.iosWidget, androidName: AppConsts.androidWidget4x1);
  }

  try {
    scheduleNextUpdate(nextPrayerTime.time);

    //TODO-fix
    // AwesomeNotifyService.clearAppNotificationsIfNoScheduledAzanOrReminders();

    BackgroundFetch.finish(taskId.toString());
  } catch (e) {
    Log.e('Error scheduling next update: $e');
  }
}

void scheduleNextUpdate(DateTime nextPrayerTime) async {
  final now = DateTime.now();
  final delay = nextPrayerTime.difference(now).inMinutes;

  if (Platform.isIOS && kDebugMode) {
    return;
  }

  if (delay > 0) {
    await BackgroundFetch.scheduleTask(
      TaskConfig(
        taskId: "com.transistorsoft.update",
        delay: delay * 60 * 1000,
        periodic: false,
        forceAlarmManager: true,
        stopOnTerminate: false,
        startOnBoot: true,
        requiresBatteryNotLow: false,
        requiresCharging: false,
        requiresDeviceIdle: false,
        requiresStorageNotLow: false,
        enableHeadless: true,
      ),
    );
  }

  await BackgroundFetch.scheduleTask(
    TaskConfig(
      taskId: "com.transistorsoft.update",
      delay: 24 * 60 * 60 * 1000,
      periodic: true,
      forceAlarmManager: true,
      stopOnTerminate: false,
      startOnBoot: true,
      requiresBatteryNotLow: false,
      requiresCharging: false,
      requiresDeviceIdle: false,
      requiresStorageNotLow: false,
      enableHeadless: true,
    ),
  );
}
