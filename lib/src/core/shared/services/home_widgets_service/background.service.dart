import 'dart:io';

import 'package:background_fetch/background_fetch.dart' hide NetworkType;
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:home_widget/home_widget.dart';
import 'package:quran_broadcast_app/src/core/consts/app_constants.dart';
import 'package:quran_broadcast_app/src/core/shared/services/home_widgets_service/home_widget.service.dart';
import 'package:quran_broadcast_app/src/features/calendar/controllers/calendar.controller.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';
import 'package:workmanager/workmanager.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../features/home/<USER>/widgets/next_prayer_times.dart';

@pragma('vm:entry-point')
void backgroundFetchHeadlessTask(taskId) async {
  WidgetsFlutterBinding.ensureInitialized();
  await GetStorageService.init();
  await HomeWidget.setAppGroupId(AppConsts.homeWidgetAppGroupId);

  final calendarController =
      ProviderContainer().read(calendarControllerNotifierProvider);

  CalendarController.calendar.value =
      await calendarController.getCalendarFromLocal();

  final now = DateTime.now();
  var currentDayData = calendarController.calendarByDate(now);
  var prayerTimes = currentDayData.prayerTimes;
  var nextPrayerTime = getNextPrayerTime(prayerTimes, date: now);

  if (nextPrayerTime.name == AppConsts.prayerNames[0]) {
    final nextDay = (now.hour >= 17 && now.hour <= 23)
        ? DateTime(now.year, now.month, now.day + 1, 0, 0)
        : now;

    Log.w('Fetching next day data for Fajr: $nextDay');

    currentDayData = calendarController.calendarByDate(nextDay);

    prayerTimes = currentDayData.prayerTimes;

    nextPrayerTime = getNextPrayerTime(prayerTimes, date: nextDay);

    Log.i(
        'Next Fajr time: ${nextPrayerTime.name}, ${nextPrayerTime.time}, Date: $nextDay');
  }

  currentDayData = calendarController.calendarByDate(now);
  prayerTimes = currentDayData.prayerTimes;

  Log.w(
      'Next prayer time: ${nextPrayerTime.name}, ${nextPrayerTime.time}, Date: $now\nDone');

  await HomeWidgetService.savePrayerTimes(prayerTimes, nextPrayerTime);
  await HomeWidget.updateWidget(
      iOSName: AppConsts.iosWidget, androidName: AppConsts.androidWidget);

  if (Platform.isAndroid) {
    await HomeWidget.updateWidget(
        iOSName: AppConsts.iosWidget, androidName: AppConsts.androidWidget4x1);
  }

  try {
    scheduleNextUpdate(nextPrayerTime.time);

    //TODO-fix
    // AwesomeNotifyService.clearAppNotificationsIfNoScheduledAzanOrReminders();

    if (taskId is String) {
      BackgroundFetch.finish(taskId);
    }
  } catch (e) {
    Log.e('Error scheduling next update: $e');
  }
}

void scheduleNextUpdate(DateTime nextPrayerTime) async {
  final now = DateTime.now();
  final delay = nextPrayerTime.difference(now).inMinutes;

  if (Platform.isIOS) {
    // We can continue even in debug mode since we're targeting iOS 14.0+

    // For iOS, we'll use WorkManager to schedule the task
    try {
      // Schedule a one-off task for the next prayer time
      if (delay > 0) {
        await Workmanager().registerOneOffTask(
          "updatePrayerWidget",
          "updatePrayerWidget",
          initialDelay: Duration(minutes: delay),
          constraints: Constraints(
            networkType: NetworkType.not_required,
            requiresBatteryNotLow: false,
            requiresCharging: false,
          ),
        );
        Log.i('iOS: Scheduled next prayer update in $delay minutes');
      }

      // Also schedule a daily task to ensure we always have updates
      await Workmanager().registerOneOffTask(
        "prayer-daily-update",
        "prayer-daily-update",
        initialDelay: const Duration(hours: 24),
        constraints: Constraints(
          networkType: NetworkType.not_required,
          requiresBatteryNotLow: false,
          requiresCharging: false,
        ),
      );
      Log.i('iOS: Scheduled daily prayer update');
    } catch (e) {
      Log.e('iOS WorkManager scheduling error: $e');
    }
    return;
  }

  // For Android, continue using BackgroundFetch
  if (delay > 0) {
    await BackgroundFetch.scheduleTask(
      TaskConfig(
        taskId: "com.transistorsoft.update",
        delay: delay * 60 * 1000,
        periodic: false,
        forceAlarmManager: true,
        stopOnTerminate: false,
        startOnBoot: true,
        requiresBatteryNotLow: false,
        requiresCharging: false,
        requiresDeviceIdle: false,
        requiresStorageNotLow: false,
        enableHeadless: true,
      ),
    );
  }

  await BackgroundFetch.scheduleTask(
    TaskConfig(
      taskId: "com.transistorsoft.update",
      delay: 24 * 60 * 60 * 1000,
      periodic: true,
      forceAlarmManager: true,
      stopOnTerminate: false,
      startOnBoot: true,
      requiresBatteryNotLow: false,
      requiresCharging: false,
      requiresDeviceIdle: false,
      requiresStorageNotLow: false,
      enableHeadless: true,
    ),
  );
}

@pragma('vm:entry-point')
void callbackDispatcherIOS() {
  Workmanager().executeTask((task, inputData) async {
    try {
      stderr.writeln("The iOS_background fetch was triggered");
      Log.i('WorkManager task started: $task');
      WidgetsFlutterBinding.ensureInitialized();
      await GetStorageService.init();
      await HomeWidget.setAppGroupId(AppConsts.homeWidgetAppGroupId);

      if (task == "updatePrayerWidget" || task == "prayer-daily-update") {
        final calendarController =
            ProviderContainer().read(calendarControllerNotifierProvider);

        CalendarController.calendar.value =
            await calendarController.getCalendarFromLocal();

        final now = DateTime.now();
        var currentDayData = calendarController.calendarByDate(now);
        var prayerTimes = currentDayData.prayerTimes;
        var nextPrayerTime = getNextPrayerTime(prayerTimes, date: now);

        if (nextPrayerTime.name == AppConsts.prayerNames[0]) {
          final nextDay = (now.hour >= 17 && now.hour <= 23)
              ? DateTime(now.year, now.month, now.day + 1, 0, 0)
              : now;

          Log.w('Fetching next day data for Fajr: $nextDay');
          currentDayData = calendarController.calendarByDate(nextDay);
          prayerTimes = currentDayData.prayerTimes;
          nextPrayerTime = getNextPrayerTime(prayerTimes, date: nextDay);
        }

        currentDayData = calendarController.calendarByDate(now);
        prayerTimes = currentDayData.prayerTimes;

        await HomeWidgetService.savePrayerTimes(prayerTimes, nextPrayerTime);
        await HomeWidget.updateWidget(
            iOSName: AppConsts.iosWidget, androidName: AppConsts.androidWidget);

        // Schedule the next update
        if (Platform.isIOS) {
          final delay = nextPrayerTime.time.difference(now).inMinutes;
          if (delay > 0) {
            await Workmanager().registerOneOffTask(
              "updatePrayerWidget",
              "updatePrayerWidget",
              initialDelay: Duration(minutes: delay),
              constraints: Constraints(
                networkType: NetworkType.not_required,
                requiresBatteryNotLow: false,
                requiresCharging: false,
              ),
            );
            Log.i('iOS: Rescheduled next prayer update in $delay minutes');
          }
        }
      }

      return Future.value(true);
    } catch (e, stackTrace) {
      Log.e('WorkManager task error: $e\n$stackTrace');
      return Future.value(false);
    }
  });
}
