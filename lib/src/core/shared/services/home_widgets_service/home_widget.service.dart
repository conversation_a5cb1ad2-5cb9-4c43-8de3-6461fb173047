import 'dart:io';

import 'package:background_fetch/background_fetch.dart' hide NetworkType;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:home_widget/home_widget.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:quran_broadcast_app/src/core/consts/app_constants.dart';
import 'package:quran_broadcast_app/src/core/shared/extensions/string_extensions.dart';
import 'package:workmanager/workmanager.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../features/calendar/models/calendar_model.dart';
import '../../../../features/calendar/providers/calendar.providers.dart';
import '../../../../features/home/<USER>/widgets/next_prayer_times.dart';
import 'background.service.dart';

class HomeWidgetService {
  static Future<void> initialize() async {
    await HomeWidget.setAppGroupId(AppConsts.homeWidgetAppGroupId);

    try {
      if (Platform.isIOS) {
        // Register home widget background callback for iOS
        await HomeWidget.registerBackgroundCallback(homeWidgetBackgroundCallback);

        // Initialize WorkManager for iOS
        await Workmanager().initialize(
          callbackDispatcher,
          isInDebugMode: true, // Set to true for debugging
        );

        // Schedule initial tasks for iOS
        await Workmanager().registerOneOffTask(
          "updatePrayerWidget",
          "updatePrayerWidget",
          initialDelay:
              const Duration(seconds: 15), // Short delay for first run
          constraints: Constraints(
            networkType: NetworkType.not_required,
            requiresBatteryNotLow: false,
            requiresCharging: false,
          ),
        );

        // Also schedule a daily task to ensure we always have updates
        await Workmanager().registerOneOffTask(
          "prayer-daily-update",
          "prayer-daily-update",
          initialDelay: const Duration(hours: 24),
          constraints: Constraints(
            networkType: NetworkType.not_required,
            requiresBatteryNotLow: false,
            requiresCharging: false,
          ),
        );

        Log.i('iOS: WorkManager and HomeWidget background callback initialized');
      } else {
        // For Android, continue using BackgroundFetch
        BackgroundFetch.configure(
          BackgroundFetchConfig(
            minimumFetchInterval: 1,
            stopOnTerminate: false,
            enableHeadless: true,
            startOnBoot: true,
            forceAlarmManager: true,
          ),
          (taskId) => backgroundFetchHeadlessTask(taskId.toString()),
        );
      }

      await Permission.ignoreBatteryOptimizations.request();
    } catch (e) {
      Log.e('Background_Permission_Error: $e');
    }
  }

  static Future<void> update(BuildContext context,
      {required WidgetRef ref}) async {
    try {
      Log.w('INIT');
      final calendarController = ref.watch(calendarControllerNotifierProvider);
      final now = DateTime.now();

      // Get current day's data
      var currentDayData = calendarController.calendarByDate(now);
      var prayerTimes = currentDayData.prayerTimes;

      //! Determine the next prayer time
      var nextPrayerTime = getNextPrayerTime(prayerTimes);

      Log.w('Current time: $now');
      Log.w('Next prayer: ${nextPrayerTime.name} at ${nextPrayerTime.time}');

      //! If next prayer is Fajr and it's still before midnight but after Isha (e.g., between 19:00 and 23:59)
      if (nextPrayerTime.name == AppConsts.prayerNames[0]) {
        final nextDay = (now.hour >= 17 && now.hour <= 23)
            ? DateTime(now.year, now.month, now.day + 1, 0, 0)
            : now;

        Log.w('Fetching next day data for Fajr: $nextDay');

        currentDayData = calendarController.calendarByDate(nextDay);

        prayerTimes = currentDayData.prayerTimes;

        nextPrayerTime = getNextPrayerTime(prayerTimes, date: nextDay);

        Log.i(
            'Next Fajr time: ${nextPrayerTime.name}, ${nextPrayerTime.time}, Date: $nextDay');
      }

      currentDayData = calendarController.calendarByDate(now);
      prayerTimes = currentDayData.prayerTimes;

      await savePrayerTimes(prayerTimes, nextPrayerTime);
      _updateWidgets();
      scheduleNextUpdate(nextPrayerTime.time);

      // AwesomeNotifyService
      //     .clearAppNotificationsIfNoScheduledAzanOrReminders();
    } catch (e, s) {
      Log.e('Widget_ERROR: $e $s');
    }
  }

  static Future<void> savePrayerTimes(
      PrayerTimeModel prayerTimes, NextPrayerTime nextPrayerTime) async {
    await HomeWidget.saveWidgetData('fajr', prayerTimes.fajr.convertTo12Hour);
    await HomeWidget.saveWidgetData(
        'sunrise', prayerTimes.sunrise.convertTo12Hour);
    await HomeWidget.saveWidgetData('dhuhr', prayerTimes.dhuhr.convertTo12Hour);
    await HomeWidget.saveWidgetData('asr', prayerTimes.asr.convertTo12Hour);
    await HomeWidget.saveWidgetData(
        'maghrib', prayerTimes.maghrib.convertTo12Hour);
    await HomeWidget.saveWidgetData('isha', prayerTimes.isha.convertTo12Hour);
    await HomeWidget.saveWidgetData('nextPrayer',
        '${nextPrayerTime.name}\n${nextPrayerTime.time.formatTime}');
    await HomeWidget.saveWidgetData('titleText',
        nextPrayerTime.name == "الشروق" ? "موعد الشروق" : "الصلاة القادمة");
  }

  static void _updateWidgets() async {
    await HomeWidget.updateWidget(
        iOSName: AppConsts.iosWidget, androidName: AppConsts.androidWidget);

    if (Platform.isAndroid) {
      await HomeWidget.updateWidget(
          iOSName: AppConsts.iosWidget,
          androidName: AppConsts.androidWidget4x1);
    }
  }
}
