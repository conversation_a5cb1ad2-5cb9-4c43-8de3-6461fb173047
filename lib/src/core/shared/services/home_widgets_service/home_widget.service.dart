import 'dart:convert';
import 'dart:io';

import 'package:background_fetch/background_fetch.dart' hide NetworkType;
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:home_widget/home_widget.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:quran_broadcast_app/src/core/consts/app_constants.dart';
import 'package:quran_broadcast_app/src/core/shared/extensions/string_extensions.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../features/calendar/controllers/calendar.controller.dart';
import '../../../../features/calendar/models/calendar_model.dart';
import '../../../../features/calendar/providers/calendar.providers.dart';
import '../../../../features/home/<USER>/widgets/next_prayer_times.dart';
import 'background.service.dart' as bg;

class HomeWidgetService {
  static Future<void> initialize() async {
    await HomeWidget.setAppGroupId(AppConsts.homeWidgetAppGroupId);

    try {
      if (Platform.isAndroid) {
        // For Android, continue using BackgroundFetch
        BackgroundFetch.configure(
          BackgroundFetchConfig(
            minimumFetchInterval: 1,
            stopOnTerminate: false,
            enableHeadless: true,
            startOnBoot: true,
            forceAlarmManager: true,
          ),
          (taskId) => bg.backgroundFetchHeadlessTask(taskId.toString()),
        );
        Log.i('Android: BackgroundFetch initialized');
      } else {
        // For iOS, we'll rely on the widget's own timeline updates
        Log.i('iOS: Using widget timeline for updates');
      }

      await Permission.ignoreBatteryOptimizations.request();
    } catch (e) {
      Log.e('Background_Permission_Error: $e');
    }
  }

  static Future<void> update(BuildContext context,
      {required WidgetRef ref}) async {
    try {
      Log.w('INIT');
      final calendarController = ref.watch(calendarControllerNotifierProvider);
      final now = DateTime.now();

      // Get current day's data
      var currentDayData = calendarController.calendarByDate(now);
      var prayerTimes = currentDayData.prayerTimes;

      //! Determine the next prayer time
      var nextPrayerTime = getNextPrayerTime(prayerTimes);

      Log.w('Current time: $now');
      Log.w('Next prayer: ${nextPrayerTime.name} at ${nextPrayerTime.time}');

      //! If next prayer is Fajr and it's still before midnight but after Isha (e.g., between 19:00 and 23:59)
      if (nextPrayerTime.name == AppConsts.prayerNames[0]) {
        final nextDay = (now.hour >= 17 && now.hour <= 23)
            ? DateTime(now.year, now.month, now.day + 1, 0, 0)
            : now;

        Log.w('Fetching next day data for Fajr: $nextDay');

        currentDayData = calendarController.calendarByDate(nextDay);

        prayerTimes = currentDayData.prayerTimes;

        nextPrayerTime = getNextPrayerTime(prayerTimes, date: nextDay);

        Log.i(
            'Next Fajr time: ${nextPrayerTime.name}, ${nextPrayerTime.time}, Date: $nextDay');
      }

      currentDayData = calendarController.calendarByDate(now);
      prayerTimes = currentDayData.prayerTimes;

      await savePrayerTimes(prayerTimes, nextPrayerTime);
      if (Platform.isIOS) {
        await saveAllPrayerTimesForWidget();
      }

      _updateWidgets();
      bg.scheduleNextUpdate(nextPrayerTime.time);

      // AwesomeNotifyService
      //     .clearAppNotificationsIfNoScheduledAzanOrReminders();
    } catch (e, s) {
      Log.e('Widget_ERROR: $e $s');
    }
  }

  static Future<void> savePrayerTimes(
      PrayerTimeModel prayerTimes, NextPrayerTime nextPrayerTime) async {
    await HomeWidget.saveWidgetData('fajr', prayerTimes.fajr.convertTo12Hour);
    await HomeWidget.saveWidgetData(
        'sunrise', prayerTimes.sunrise.convertTo12Hour);
    await HomeWidget.saveWidgetData('dhuhr', prayerTimes.dhuhr.convertTo12Hour);
    await HomeWidget.saveWidgetData('asr', prayerTimes.asr.convertTo12Hour);
    await HomeWidget.saveWidgetData(
        'maghrib', prayerTimes.maghrib.convertTo12Hour);
    await HomeWidget.saveWidgetData('isha', prayerTimes.isha.convertTo12Hour);
    await HomeWidget.saveWidgetData('nextPrayer',
        '${nextPrayerTime.name}\n${nextPrayerTime.time.formatTime}');
    await HomeWidget.saveWidgetData('titleText',
        nextPrayerTime.name == "الشروق" ? "موعد الشروق" : "الصلاة القادمة");
  }

  static Future<void> saveAllPrayerTimesForWidget() async {
    try {
      final calendarData = CalendarController.calendarBySummerTime;

      if (calendarData.isEmpty) {
        Log.w('No calendar data available for widget');
        return;
      }

      // Create a map of all prayer times for the next 30 days
      final Map<String, Map<String, String>> allPrayerTimes = {};

      for (final calendar in calendarData) {
        for (final day in calendar.days) {
          if (day.gregorianDate.isNotEmpty && day.prayerTimes.fajr.isNotEmpty) {
            allPrayerTimes[day.gregorianDate] = {
              'fajr': day.prayerTimes.fajr.convertTo12Hour,
              'dhuhr': day.prayerTimes.dhuhr.convertTo12Hour,
              'asr': day.prayerTimes.asr.convertTo12Hour,
              'maghrib': day.prayerTimes.maghrib.convertTo12Hour,
              'isha': day.prayerTimes.isha.convertTo12Hour,
              'sunrise': day.prayerTimes.sunrise.convertTo12Hour,
            };
          }
        }
      }

      // Save all prayer times as JSON string
      await HomeWidget.saveWidgetData(
          'allPrayerTimes', jsonEncode(allPrayerTimes));
      final calendarController =
          ProviderContainer().read(calendarControllerNotifierProvider);

      // Also save current day's prayer times for immediate use
      final now = DateTime.now();
      final currentDayData = calendarController.calendarByDate(now);
      final prayerTimes = currentDayData.prayerTimes;
      final nextPrayerTime = getNextPrayerTime(prayerTimes, date: now);

      await savePrayerTimes(prayerTimes, nextPrayerTime);

      Log.i('Saved ${allPrayerTimes.length} days of prayer times for widget');
    } catch (e, s) {
      Log.e('Error saving all prayer times for widget: $e\n$s');
    }
  }

  static void _updateWidgets() async {
    await HomeWidget.updateWidget(
        iOSName: AppConsts.iosWidget, androidName: AppConsts.androidWidget);

    if (Platform.isAndroid) {
      await HomeWidget.updateWidget(
          iOSName: AppConsts.iosWidget,
          androidName: AppConsts.androidWidget4x1);
    }
  }
}
