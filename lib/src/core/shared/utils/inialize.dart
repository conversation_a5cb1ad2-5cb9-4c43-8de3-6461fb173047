import 'dart:io';

import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/services.dart';
import 'package:flutter_quran/flutter_quran.dart';
import 'package:just_audio_background/just_audio_background.dart';
import 'package:quran_broadcast_app/firebase_options.dart';
import 'package:quran_broadcast_app/src/features/notifications/service/ios_notification_service.dart';
import 'package:xr_helper/xr_helper.dart';

Future<void> initialize() async {
  await GetStorageService.init();

  SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  HttpOverrides.global = MyHttpOverrides();

  await AwesomeNotifyService.requestPermissions();

  // final isAllowed = await AwesomeNotifications().isNotificationAllowed();
  //
  // if (!isAllowed) {
  //   await AwesomeNotifications().requestPermissionToSendNotifications(
  //     permissions: [
  //       NotificationPermission.Alert,
  //       NotificationPermission.Badge,
  //       NotificationPermission.Sound,
  //     ],
  //   );
  // }

  // await AwesomeNotifications().initialize(
  //   'resource://mipmap/ic_launcher',
  //   [
  //     NotificationChannel(
  //       channelKey: AppConsts.azanChannelKey,
  //       channelName: 'Scheduled Notifications',
  //       soundSource: 'resource://raw/res_azan',
  //       channelDescription: 'Notification channel for scheduled notifications',
  //       defaultColor: const Color(0xFFFFFFFF),
  //       ledColor: Colors.white,
  //       importance: NotificationImportance.Max,
  //       playSound: true,
  //     ),
  //     NotificationChannel(
  //       channelKey: AppConsts.reminderChannelKey,
  //       channelName: 'Reminder Notifications',
  //       soundSource: 'resource://raw/res_reminder',
  //       channelDescription: 'Notification channel for reminder notifications',
  //       defaultColor: const Color(0xFFFFFFFF),
  //       ledColor: Colors.white,
  //       importance: NotificationImportance.Max,
  //       playSound: true,
  //     ),
  //     NotificationChannel(
  //       channelKey: AppConsts.shroukChannelKey,
  //       channelName: 'Shrouk Notifications',
  //       // soundSource: 'resource://raw/res_reminder',
  //       channelDescription: 'Notification channel for shrouk notifications',
  //       defaultColor: const Color(0xFFFFFFFF),
  //       ledColor: Colors.white,
  //       importance: NotificationImportance.Max,
  //       playSound: true,
  //     ),
  //   ],
  // );

  await JustAudioBackground.init(
    androidNotificationChannelId: 'com.ryanheise.bg_demo.channel.audio',
    androidNotificationChannelName: 'Audio playback',
    androidNotificationOngoing: true,
  );

  await FlutterQuran().init();

  // BackgroundFetch.registerHeadlessTask(callbackDispatcherHomeWidget);
  //
  // HomeWidgetService.initialize();

  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  NotificationService.init();
}
