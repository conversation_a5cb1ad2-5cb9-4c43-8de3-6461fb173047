import 'package:flutter/material.dart';
import 'package:quran_broadcast_app/src/features/notifications/service/ios_notification_service.dart';

class NotificationExample extends StatelessWidget {
  const NotificationExample({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Notification Example'),
      ),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // ElevatedButton(
            //   onPressed: () {
            //     // Show an immediate notification
            //     // AwesomeNotifyService.showNotification(
            //     //   title: 'Test Notification',
            //     //   description: 'This is a test notification',
            //     //   sound: 'res_azan',
            //     // );
            //   },
            //   child: const Text('Show Immediate Notification'),
            // ),
            // const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                // Schedule a notification for 10 seconds from now
                final now = DateTime.now();
                final scheduledTime = now.add(const Duration(seconds: 5));

                AwesomeNotifyService.createScheduleNotification(
                    id: AwesomeNotifyService.createUniqueId('test', now),
                    title: 'Scheduled Notification',
                    body: 'This notification was scheduled 5 seconds ago',
                    dateTime: scheduledTime,
                    sound: 'res_reminder',
                    isReminder: true);
              },
              child: const Text('Schedule Notification (5 seconds)'),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () async {
                // List all scheduled notifications
                final scheduledNotifications =
                    await AwesomeNotifyService.listScheduledNotifications();

                // Show a dialog with the list of scheduled notifications
                if (context.mounted) {
                  showDialog(
                    context: context,
                    builder: (context) => AlertDialog(
                      title: const Text('Scheduled Notifications'),
                      content: SizedBox(
                        width: double.maxFinite,
                        child: ListView.builder(
                          shrinkWrap: true,
                          itemCount: scheduledNotifications.length,
                          itemBuilder: (context, index) {
                            final notification = scheduledNotifications[index];
                            return ListTile(
                              title: Text('ID: ${notification.id}'),
                              subtitle: Text(notification.title ?? 'No title'),
                            );
                          },
                        ),
                      ),
                      actions: [
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child: const Text('Close'),
                        ),
                      ],
                    ),
                  );
                }
              },
              child: const Text('List Scheduled Notifications'),
            ),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                // Cancel all scheduled notifications
                AwesomeNotifyService.cancelAllScheduledNotifications();
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('All notifications cancelled'),
                  ),
                );
              },
              child: const Text('Cancel All Notifications'),
            ),
          ],
        ),
      ),
    );
  }
}
