import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:gif/gif.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:quran_broadcast_app/generated/assets.gen.dart';
import 'package:quran_broadcast_app/src/core/shared/services/home_widgets_service/home_widget.service.dart';
import 'package:quran_broadcast_app/src/core/theme/color_manager.dart';
import 'package:quran_broadcast_app/src/features/calendar/controllers/calendar.controller.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';
import 'package:quran_broadcast_app/src/features/notifications/controller/notification_controller.dart';
import 'package:quran_broadcast_app/src/features/settings/models/settings_model.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../core/consts/app_constants.dart';
import '../../audio_stream/view/audio_stream_screen.dart';
import '../../main_screen/view/main.screen.dart';
import '../../settings/providers/settings.providers.dart';

class SplashScreen extends HookConsumerWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsController = ref.watch(settingsControllerNotifierProvider);
    final calendarController = ref.watch(calendarControllerNotifierProvider);
    final notificationController =
        ref.watch(notificationControllerProvider(ref));

    final lastUpdateLocalTime = DateTime.tryParse(
      GetStorageService.getData(key: LocalKeys.lastUpdateLocalTime) ?? '',
    );

    Future<void> preloadAudio() async {
      try {
        final url = settingsController.settings.value.live.url.isEmpty
            ? AppConsts.liveUrl
            : settingsController.settings.value.live.url;

        await globalAudioPlayerHandler.setUrl(url);
      } catch (e) {
        debugPrint('Error preloading audio: $e');
        hasInitializedAudio = true;
      }
    }

    void init() async {
      Future.microtask(() async {
        try {
          final localSettings = await settingsController.getSettingsFromLocal();

          const timeoutDuration = Duration(seconds: 10);

          CalendarController.calendar.value =
              await calendarController.getCalendarFromLocal();

          if (localSettings != SettingsModel.empty() &&
              CalendarController.calendar.value.isNotEmpty) {
            final settings =
                await settingsController.getSettings().timeout(timeoutDuration);

            final isLastUpdateSameOfLastSavedLocalUpdateTime =
                settings.lastUpdatedAt.formatDateToStringWithTime ==
                    lastUpdateLocalTime.formatDateToStringWithTime;

            if (!isLastUpdateSameOfLastSavedLocalUpdateTime) {
              await calendarController
                  .getCalendar(
                      date: DateTime.now().formatDateToString,
                      overrideLocalData: true)
                  .timeout(timeoutDuration);
            } else {
              await Future.delayed(const Duration(seconds: 3));
            }
          } else {
            await settingsController.getSettings().timeout(timeoutDuration);
            CalendarController.calendar.value = await calendarController
                .getCalendar(date: DateTime.now().formatDateToString)
                .timeout(timeoutDuration);
          }

          // notificationController.cancelAllScheduledNotifications();
          notificationController.schedulePrayerNotifications();

          // Save all prayer times for widget after calendar data is loaded
          if (Platform.isIOS) {
            HomeWidgetService.saveIOSAllPrayerTimesForWidget();
          }

          await precacheImage(
              const AssetImage('assets/images/banner.webp'), context);

          preloadAudio();

          // final dateTime = DateTime.now().add(
          //   const Duration(seconds: 5),
          // );
          // await AndroidAwesomeNotifyService.createScheduleNotification(
          //   id: AndroidAwesomeNotifyService.createUniqueId(
          //     AppConsts.prayerNames[0],
          //     dateTime,
          //   ),
          //   title: "وقت الصلاة",
          //   body: "حان الآن موعد أذان ${AppConsts.prayerNames[0]}",
          //   dateTime: dateTime,
          //   sound: "resource://raw/res_azan",
          // );

          // NotificationExample().navigateReplacement;
          const MainScreen().navigateReplacement;
        } catch (e, s) {
          Log.e('Splash_Error: $e, $s');
        }

        // final timeZone =
        //     await AwesomeNotifications().getLocalTimeZoneIdentifier();
        // AwesomeNotifications().createNotification(
        //   content: NotificationContent(
        //     id: 1,
        //     channelKey: AppConsts.azanChannelKey,
        //     title: 'Test Notification',
        //     body: 'This is a test of the azan sound',
        //     customSound: 'resource://res_azan',
        //     // 'resource://raw/res_azan',
        //   ),
        //   schedule: NotificationCalendar(
        //     day: dateTime.day,
        //     month: dateTime.month,
        //     year: dateTime.year,
        //     hour: dateTime.hour,
        //     minute: dateTime.minute + 1,
        //     second: 0,
        //     allowWhileIdle: true,
        //     repeats: false,
        //     timeZone: timeZone,
        //   ),
        // );
      });
    }

    useEffect(() {
      init();

      return () {};
    }, []);

    return Scaffold(
      backgroundColor: ColorManager.backgroundColor,
      body: Gif(
        image: AssetImage(Assets.images.splash.path),
        height: double.infinity,
        width: double.infinity,
        fit: BoxFit.cover,
        repeat: ImageRepeat.repeat,
        autostart: Autostart.loop,
        fps: 12,
      ),
    );
  }
}
