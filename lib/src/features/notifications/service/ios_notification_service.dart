import 'package:awesome_notifications/awesome_notifications.dart';
import 'package:eraser/eraser.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:quran_broadcast_app/src/core/consts/app_constants.dart';
import 'package:xr_helper/xr_helper.dart';

class AwesomeNotifyService {
  static int createUniqueId(String prayerName, DateTime dateTime) {
    final id = prayerName.hashCode ^ dateTime.hashCode;
    return id;
  }

  static Future<bool> createScheduleNotification({
    required int id,
    required DateTime dateTime,
    bool isReminder = false,
    String? title,
    String? body,
    String? sound,
  }) async {
    // if (kDebugMode) return true;

    final isAllowed = await AwesomeNotifications().isNotificationAllowed();

    final isShrouk = title == "وقت الشروق";

    final channelKey = isShrouk
        ? AppConsts.shroukChannelKey
        : isReminder
            ? AppConsts.reminderChannelKey
            : AppConsts.azanChannelKey;

    final timeZone = await AwesomeNotifications().getLocalTimeZoneIdentifier();

    if (isAllowed) {
      Log.w('Creating_Schedule_Notification: $id\n'
          'Title: $title\n'
          'Body: $body\n'
          'DateTime: ${dateTime.formatDateToStringWithTime}\n'
          'ChannelKey: $channelKey\n'
          // 'Sound: $sound\n'
          'TimeZone: $timeZone');
      return await AwesomeNotifications().createNotification(
        content: NotificationContent(
          id: id,
          channelKey: channelKey,
          criticalAlert: true,
          category: NotificationCategory.Status,
          title: title,
          body: body,
          payload: {
            'scheduledTime': dateTime.toIso8601String(),
          },
        ),
        schedule: NotificationCalendar(
          day: dateTime.day,
          month: dateTime.month,
          year: dateTime.year,
          hour: dateTime.hour,
          minute: dateTime.minute,
          second: 0,
          allowWhileIdle: true,
          repeats: false,
          timeZone: timeZone,
        ),
      );
    } else {
      await AwesomeNotifications().requestPermissionToSendNotifications();
    }

    return isAllowed;
  }

  static Future<void> cancelScheduledNotificationById(int id) async {
    await AwesomeNotifications().cancel(id);
  }

  static Future<void> cancelAllScheduledNotifications() async {
    await AwesomeNotifications().cancelAll();
    await AwesomeNotifications().cancelAllSchedules();
    try {
      await FlutterLocalNotificationsPlugin()
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.cancelAll();
      await FlutterLocalNotificationsPlugin().cancelAll();
      Log.w('Cancelinfffffapp notifications');
    } catch (e) {
      Log.e('Error_Cancel app notifications: $e');
    }
  }

  //listScheduledNotifications
  static Future<List<NotificationModel>> listScheduledNotifications() async {
    return await AwesomeNotifications().listScheduledNotifications();
  }

  static Future<void>
      clearAppNotificationsIfNoScheduledAzanOrReminders() async {
    final now = DateTime.now();
    const bufferDuration = Duration(minutes: 3);

    // Retrieve all scheduled notifications
    final scheduledNotifications =
        await AwesomeNotifyService.listScheduledNotifications();

    // Check if any scheduled notification is within the 3-minute window
    final hasUpcomingNotifications = scheduledNotifications.any((notification) {
      final scheduledTime = DateTime.tryParse(
          notification.content?.payload?['scheduledTime'] ?? '');

      if (scheduledTime == null) return false;

      return scheduledTime.isAfter(now.subtract(bufferDuration)) &&
          scheduledTime.isBefore(now.add(bufferDuration));
    });

    // Clear notifications only if no upcoming Azan or reminders
    if (!hasUpcomingNotifications) {
      Eraser.clearAllAppNotifications();
      // Log.w('Cleared all app notifications as no Azan or reminders are scheduled.');
    } else {
      // Log.w('Skipped clearing notifications due to upcoming Azan or reminders.');
    }
  }
}

// import 'dart:developer';
//
// import 'package:flutter_local_notifications/flutter_local_notifications.dart';
// import 'package:quran_broadcast_app/src/core/consts/app_constants.dart';
// import 'package:timezone/data/latest.dart' as tz;
// import 'package:timezone/timezone.dart' as tz;
// import 'package:xr_helper/xr_helper.dart';
//
// import 'notification_service.dart';
//
// @pragma('vm:entry-point')
// Future<void> onDidReceiveBackgroundNotificationResponse(
//     NotificationResponse response) async {
//   // You can use print or your logger here
//   log('onDidReceiveBackgroundNotificationResponse: ${response.id}');
// }
//
// class AwesomeNotifyService {
//   static final FlutterLocalNotificationsPlugin
//       _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();
//
//   static bool _initialized = false;
//
//   static Future<void> initialize() async {
//     // if (_initialized) return;
//
//     tz.initializeTimeZones();
//
//     const AndroidInitializationSettings initializationSettingsAndroid =
//         AndroidInitializationSettings('@mipmap/ic_launcher');
//
//     const DarwinInitializationSettings initializationSettingsIOS =
//         DarwinInitializationSettings(
//       requestAlertPermission: true,
//       requestBadgePermission: true,
//       requestSoundPermission: true,
//     );
//
//     const InitializationSettings initializationSettings =
//         InitializationSettings(
//       android: initializationSettingsAndroid,
//       iOS: initializationSettingsIOS,
//     );
//
//     await _flutterLocalNotificationsPlugin.initialize(
//       initializationSettings,
//       onDidReceiveNotificationResponse: (NotificationResponse response) {
//         Log.w('onDidReceiveNotificationResponse: ${response.id}');
//         // Handle notification tap
//       },
//       onDidReceiveBackgroundNotificationResponse:
//           onDidReceiveBackgroundNotificationResponse,
//     );
//
//     // Configure notification channels
//     await _configureNotificationChannels();
//
//     _initialized = true;
//   }
//
//   static Future<void> _configureNotificationChannels() async {
//     // Create prayer notification channel
//     const AndroidNotificationChannel azanChannel = AndroidNotificationChannel(
//       AppConsts.azanChannelKey,
//       'Prayer Times Notifications',
//       description: 'Notifications for prayer times',
//       importance: Importance.max,
//       playSound: true,
//       sound: RawResourceAndroidNotificationSound('res_azan'),
//     );
//
//     // Create shrouk notification channel
//     const AndroidNotificationChannel shroukChannel = AndroidNotificationChannel(
//       AppConsts.shroukChannelKey,
//       'Shrouk Time Notifications',
//       description: 'Notifications for Shrouk time',
//       importance: Importance.high,
//       playSound: true,
//     );
//
//     // Create reminder notification channel
//     const AndroidNotificationChannel reminderChannel =
//         AndroidNotificationChannel(
//       AppConsts.reminderChannelKey,
//       'Prayer Reminder Notifications',
//       description: 'Reminders for prayer times',
//       importance: Importance.high,
//       playSound: true,
//       sound: RawResourceAndroidNotificationSound('res_reminder'),
//     );
//
//     await _flutterLocalNotificationsPlugin
//         .resolvePlatformSpecificImplementation<
//             AndroidFlutterLocalNotificationsPlugin>()
//         ?.createNotificationChannel(azanChannel);
//
//     await _flutterLocalNotificationsPlugin
//         .resolvePlatformSpecificImplementation<
//             AndroidFlutterLocalNotificationsPlugin>()
//         ?.createNotificationChannel(shroukChannel);
//
//     await _flutterLocalNotificationsPlugin
//         .resolvePlatformSpecificImplementation<
//             AndroidFlutterLocalNotificationsPlugin>()
//         ?.createNotificationChannel(reminderChannel);
//   }
//
//   static int createUniqueId(String prayerName, DateTime dateTime) {
//     final id = prayerName.hashCode ^ dateTime.hashCode;
//     return id.abs(); // Ensure positive ID value
//   }
//
//   static Future<bool> createScheduleNotification({
//     required int id,
//     required DateTime dateTime,
//     bool isReminder = false,
//     String? title,
//     String? body,
//     String? sound,
//   }) async {
//     // Ensure the notification service is initialized
//     await initialize();
//
//     // Skip if date is in the past
//     if (dateTime.isBefore(DateTime.now())) {
//       return false;
//     }
//
//     Log.w('Creating_Schedule_Notification: $id\n'
//         'Title: $title\n'
//         'Body: $body\n'
//         'DateTime: ${dateTime.formatDateToStringWithTime}\n'
//         'Sound: $sound\n');
//
//     // if (kDebugMode) return true;
//
//     final isShrouk = title == "وقت الشروق";
//
//     final String channelId = isShrouk
//         ? AppConsts.shroukChannelKey
//         : isReminder
//             ? AppConsts.reminderChannelKey
//             : AppConsts.azanChannelKey;
//
//     try {
//       // Convert DateTime to TZDateTime
//       final scheduledDate = tz.TZDateTime.from(dateTime, tz.local);
//
//       // Configure notification details
//       AndroidNotificationDetails androidDetails = AndroidNotificationDetails(
//         channelId,
//         isShrouk
//             ? 'Shrouk Notification'
//             : isReminder
//                 ? 'Prayers Reminder'
//                 : 'Prayers Time',
//         channelDescription: 'Channel for prayer time notifications',
//         importance: isReminder ? Importance.high : Importance.max,
//         priority: isReminder ? Priority.high : Priority.max,
//         sound:
//             sound != null ? RawResourceAndroidNotificationSound(sound) : null,
//         enableLights: true,
//         playSound: true,
//         fullScreenIntent: !isReminder,
//         // Full screen for prayer times
//         category: isReminder
//             ? AndroidNotificationCategory.reminder
//             : AndroidNotificationCategory.alarm,
//       );
//
//       DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
//         presentAlert: true,
//         presentBadge: true,
//         presentSound: true,
//         sound: '$sound.caf',
//         interruptionLevel: isReminder
//             ? InterruptionLevel.active
//             : InterruptionLevel.timeSensitive,
//       );
//
//       NotificationDetails notificationDetails = NotificationDetails(
//         android: androidDetails,
//         iOS: iosDetails,
//       );
//
//       // Schedule the notification
//       await _flutterLocalNotificationsPlugin.zonedSchedule(
//         id,
//         title,
//         body,
//         scheduledDate,
//         notificationDetails,
//         androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
//         matchDateTimeComponents: isReminder ? null : DateTimeComponents.time,
//       );
//
//       return true;
//     } catch (e) {
//       print('Error scheduling notification: $e');
//       return false;
//     }
//   }
//
//   static Future<void> cancelScheduledNotificationById(int id) async {
//     await _flutterLocalNotificationsPlugin.cancel(id);
//   }
//
//   static Future<void> cancelAllScheduledNotifications() async {
//     await _flutterLocalNotificationsPlugin.cancelAll();
//     await AndroidAwesomeNotifyService.cancelAllScheduledNotifications();
//   }
//
//   static Future<bool> requestPermissions() async {
//     final AndroidFlutterLocalNotificationsPlugin? androidImplementation =
//         _flutterLocalNotificationsPlugin.resolvePlatformSpecificImplementation<
//             AndroidFlutterLocalNotificationsPlugin>();
//
//     final bool? androidPermissionGranted =
//         await androidImplementation?.requestNotificationsPermission();
//
//     final bool? iosPermissionGranted = await _flutterLocalNotificationsPlugin
//         .resolvePlatformSpecificImplementation<
//             IOSFlutterLocalNotificationsPlugin>()
//         ?.requestPermissions(
//           alert: true,
//           badge: true,
//           sound: true,
//         );
//
//     return androidPermissionGranted ?? iosPermissionGranted ?? false;
//   }
//
//   // static Future<void> showNotification({
//   //   required String title,
//   //   required String description,
//   //   String? route,
//   //   String? sound,
//   // }) async {
//   //   // Ensure the notification service is initialized
//   //   await initialize();
//   //
//   //   // Android Notification Configuration
//   //   AndroidNotificationDetails androidPlatformChannelSpecifics =
//   //       AndroidNotificationDetails(
//   //     AppConsts.azanChannelKey,
//   //     // Using the azan channel for immediate notifications
//   //     'Prayer Time',
//   //     channelDescription: 'Channel for prayer time notification',
//   //     playSound: true,
//   //     sound: sound != null ? RawResourceAndroidNotificationSound(sound) : null,
//   //     importance: Importance.max,
//   //     priority: Priority.max,
//   //     styleInformation: BigTextStyleInformation(
//   //       description,
//   //       contentTitle: title,
//   //       summaryText: description,
//   //     ),
//   //     enableVibration: false,
//   //   );
//   //
//   //   // iOS Notification Configuration
//   //   DarwinNotificationDetails iOSPlatformChannelSpecifics =
//   //       DarwinNotificationDetails(
//   //     presentAlert: true,
//   //     presentBadge: true,
//   //     presentSound: true,
//   //     sound: '$sound.caf',
//   //     interruptionLevel: InterruptionLevel.timeSensitive,
//   //   );
//   //
//   //   NotificationDetails platformChannelSpecifics = NotificationDetails(
//   //       android: androidPlatformChannelSpecifics,
//   //       iOS: iOSPlatformChannelSpecifics);
//   //
//   //   // Show the notification
//   //   await _flutterLocalNotificationsPlugin.show(
//   //     DateTime.now().millisecond, // Use current time millisecond as a unique ID
//   //     title,
//   //     description,
//   //     platformChannelSpecifics,
//   //     payload: route,
//   //   );
//   // }
//
//   static Future<List<PendingNotificationRequest>>
//       listScheduledNotifications() async {
//     return await _flutterLocalNotificationsPlugin.pendingNotificationRequests();
//   }
// }
