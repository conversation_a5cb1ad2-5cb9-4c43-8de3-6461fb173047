import 'dart:io';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:quran_broadcast_app/src/core/consts/app_constants.dart';
import 'package:quran_broadcast_app/src/features/calendar/controllers/calendar.controller.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';
import 'package:quran_broadcast_app/src/features/notifications/service/android_notification_service.dart';
import 'package:string_validator/string_validator.dart';
import 'package:xr_helper/xr_helper.dart';

final notificationControllerProvider =
    ChangeNotifierProvider.family<NotificationController, WidgetRef>(
  (_, ref) => NotificationController(ref),
);

class NotificationController extends BaseVM {
  // final GetStorage _storage = GetStorage();
  final WidgetRef ref;

  List<bool> notificationStates = [];
  List<String> reminderTimes = [];

  NotificationController(this.ref) {
    _loadNotificationSettings();
  }

  void _loadNotificationSettings() {
    baseFunction(
      () {
        final prayerTimes = _getPrayerTimes();
        notificationStates = List.generate(prayerTimes.length, (index) {
          return GetStorageService.getData(key: 'notification_$index') ??
              true; // Default to true
        });
        reminderTimes = List.generate(prayerTimes.length, (index) {
          return GetStorageService.getData(key: 'reminder_$index') ?? '';
        });
      },
    );
  }

  void toggleNotification(int index) {
    baseFunction(
      () {
        notificationStates[index] = !notificationStates[index];
      },
    );
  }

  void saveSettingsAndScheduleNotifications() {
    baseFunction(
      () {
        for (int i = 0; i < notificationStates.length; i++) {
          GetStorageService.setData(
              key: 'notification_$i', value: notificationStates[i]);
          GetStorageService.setData(
              key: 'reminder_$i', value: reminderTimes[i]);
        }
        schedulePrayerNotifications();
      },
    );
  }

  void setReminderTime(int index, String time) {
    baseFunction(
      () {
        reminderTimes[index] = time;
      },
    );
  }

  void schedulePrayerNotifications() async {
    try {
      await Permission.scheduleExactAlarm.request();
      await AndroidAwesomeNotifyService.cancelAllScheduledNotifications();
    } catch (e) {
      Log.e('Permission Error: $e');
    }

    final futureDays = CalendarController.calendarBySummerTime
        .expand((calendar) => calendar.days);

    final reminderDurations = {
      "قبل 5 دقائق": const Duration(minutes: 5),
      "قبل 10 دقائق": const Duration(minutes: 10),
      "قبل 15 دقيقة": const Duration(minutes: 15),
      "قبل 20 دقيقة": const Duration(minutes: 20),
    };

    // Counter for iOS notifications
    int scheduledNotificationsCount = 0;
    const int maxIOSNotifications = 60;

    // Notifications to schedule
    final notifications = <Map<String, dynamic>>[];

    // First collect all possible notifications
    for (final day in futureDays) {
      final prayerTimes = [
        day.prayerTimes.fajr,
        day.prayerTimes.sunrise,
        day.prayerTimes.dhuhr,
        day.prayerTimes.asr,
        day.prayerTimes.maghrib,
        day.prayerTimes.isha,
      ];

      for (int i = 0; i < prayerTimes.length; i++) {
        if (notificationStates[i]) {
          final prayerTime = prayerTimes[i].split(":");

          if (prayerTime.length < 2) {
            continue;
          }

          final prayerDateTime = DateTime(
            day.gregorianYearNumber.toInt().toInt(),
            day.gregorianMonthNumber.toInt().toInt(),
            day.gregorianDayNumber.toInt().toInt(),
            int.parse(prayerTime[0]),
            int.parse(prayerTime[1]),
          );

          if (prayerDateTime.isBefore(DateTime.now())) {
            continue;
          }

          final isShrouk = i == 1;

          // Add prayer notification to queue
          notifications.add({
            'isReminder': false,
            'id': AndroidAwesomeNotifyService.createUniqueId(
              AppConsts.prayerNames[i],
              prayerDateTime,
            ),
            'title': isShrouk ? "وقت الشروق" : "وقت الصلاة",
            'body': isShrouk
                ? "حان الآن موعد الشروق"
                : "حان الآن موعد أذان ${AppConsts.prayerNames[i]}",
            'dateTime': prayerDateTime,
            'sound': "resource://raw/res_azan",
          });

          // Add reminder notification to queue if set
          if (reminderTimes[i].isNotEmpty) {
            final reminderDuration =
                reminderDurations[reminderTimes[i]] ?? const Duration();
            final reminderDateTime = prayerDateTime.subtract(reminderDuration);

            if (reminderDateTime.isBefore(DateTime.now())) {
              continue;
            }

            notifications.add({
              'isReminder': true,
              'id': AndroidAwesomeNotifyService.createUniqueId(
                AppConsts.prayerNames[i],
                reminderDateTime,
              ),
              'title': isShrouk ? "تذكير الشروق" : "تذكير الصلاة",
              'body': isShrouk
                  ? "تبقى ${reminderTimes[i]} على الشروق"
                  : "تبقى ${reminderTimes[i]} على أذان ${AppConsts.prayerNames[i]}"
                      .replaceAll("قبل ", ""),
              'dateTime': reminderDateTime,
              'sound': "resource://raw/res_reminder",
            });
          }
        }
      }
    }

    // Sort by date (schedule nearest first)
    notifications.sort((a, b) =>
        (a['dateTime'] as DateTime).compareTo(b['dateTime'] as DateTime));

    // Schedule notifications with iOS limit
    for (final notification in notifications) {
      // Check iOS notification limit
      if (Platform.isIOS &&
          scheduledNotificationsCount >= maxIOSNotifications) {
        Log.w(
            'iOS notification limit reached ($maxIOSNotifications). Skipping remaining notifications.');
        break;
      }

      await AndroidAwesomeNotifyService.createScheduleNotification(
        isReminder: notification['isReminder'],
        id: notification['id'],
        title: notification['title'],
        body: notification['body'],
        dateTime: notification['dateTime'],
        sound: notification['sound'],
      );

      scheduledNotificationsCount++;
    }

    Log.w('Total scheduled notifications: $scheduledNotificationsCount');
    Log.w(
        'Schedule_Notifications for Prayer Times: $notificationStates\nReminder_Times: $reminderTimes');
  }

  List<String> _getPrayerTimes() {
    final calendarController = ref.read(calendarControllerNotifierProvider);
    final currentDayData = calendarController.currentDayCalendar;
    return [
      currentDayData.prayerTimes.fajr,
      currentDayData.prayerTimes.sunrise,
      currentDayData.prayerTimes.dhuhr,
      currentDayData.prayerTimes.asr,
      currentDayData.prayerTimes.maghrib,
      currentDayData.prayerTimes.isha,
    ];
  }

  Future<void> cancelAllScheduledNotifications() async {
    await AndroidAwesomeNotifyService.cancelAllScheduledNotifications();
  }
}

import 'dart:io';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:quran_broadcast_app/src/core/consts/app_constants.dart';
import 'package:quran_broadcast_app/src/features/calendar/controllers/calendar.controller.dart';
import 'package:quran_broadcast_app/src/features/calendar/providers/calendar.providers.dart';
import 'package:quran_broadcast_app/src/features/notifications/service/android_notification_service.dart';
import 'package:quran_broadcast_app/src/features/notifications/service/ios_notification_service.dart';
import 'package:string_validator/string_validator.dart';
import 'package:xr_helper/xr_helper.dart';

final notificationControllerProvider =
    ChangeNotifierProvider.family<NotificationController, WidgetRef>(
  (_, ref) => NotificationController(ref),
);

class NotificationController extends BaseVM {
  // final GetStorage _storage = GetStorage();
  final WidgetRef ref;

  List<bool> notificationStates = [];
  List<String> reminderTimes = [];

  NotificationController(this.ref) {
    _loadNotificationSettings();
  }

  void _loadNotificationSettings() {
    baseFunction(
      () {
        final prayerTimes = _getPrayerTimes();
        notificationStates = List.generate(prayerTimes.length, (index) {
          return GetStorageService.getData(key: 'notification_$index') ??
              true; // Default to true
        });
        reminderTimes = List.generate(prayerTimes.length, (index) {
          return GetStorageService.getData(key: 'reminder_$index') ?? '';
        });
      },
    );
  }

  void toggleNotification(int index) {
    baseFunction(
      () {
        notificationStates[index] = !notificationStates[index];
      },
    );
  }

  void saveSettingsAndScheduleNotifications() {
    baseFunction(
      () {
        for (int i = 0; i < notificationStates.length; i++) {
          GetStorageService.setData(
              key: 'notification_$i', value: notificationStates[i]);
          GetStorageService.setData(
              key: 'reminder_$i', value: reminderTimes[i]);
        }
        schedulePrayerNotifications();
      },
    );
  }

  void setReminderTime(int index, String time) {
    baseFunction(
      () {
        reminderTimes[index] = time;
      },
    );
  }

  void schedulePrayerNotifications() async {
    try {
      await Permission.scheduleExactAlarm.request();
      if(Platform.isIOS) {
        await IOSAwesomeNotifyService.cancelAllScheduledNotifications();
      } else {
        await AndroidAwesomeNotifyService.cancelAllScheduledNotifications();
      }
    } catch (e) {
      Log.e('Permission Error: $e');
    }

    final futureDays = CalendarController.calendarBySummerTime
        .expand((calendar) => calendar.days);

    final reminderDurations = {
      "قبل 5 دقائق": const Duration(minutes: 5),
      "قبل 10 دقائق": const Duration(minutes: 10),
      "قبل 15 دقيقة": const Duration(minutes: 15),
      "قبل 20 دقيقة": const Duration(minutes: 20),
    };

    // Counter for iOS notifications
    int scheduledNotificationsCount = 0;
    const int maxIOSNotifications = 60;

    // Notifications to schedule
    final notifications = <Map<String, dynamic>>[];

    // First collect all possible notifications
    for (final day in futureDays) {
      final prayerTimes = [
        day.prayerTimes.fajr,
        day.prayerTimes.sunrise,
        day.prayerTimes.dhuhr,
        day.prayerTimes.asr,
        day.prayerTimes.maghrib,
        day.prayerTimes.isha,
      ];

      for (int i = 0; i < prayerTimes.length; i++) {
        if (notificationStates[i]) {
          final prayerTime = prayerTimes[i].split(":");

          if (prayerTime.length < 2) {
            continue;
          }

          final prayerDateTime = DateTime(
            day.gregorianYearNumber.toInt().toInt(),
            day.gregorianMonthNumber.toInt().toInt(),
            day.gregorianDayNumber.toInt().toInt(),
            int.parse(prayerTime[0]),
            int.parse(prayerTime[1]),
          );

          if (prayerDateTime.isBefore(DateTime.now())) {
            continue;
          }

          final isShrouk = i == 1;

          // Add prayer notification to queue
          notifications.add({
            'isReminder': false,
            'id': AndroidAwesomeNotifyService.createUniqueId(
              AppConsts.prayerNames[i],
              prayerDateTime,
            ),
            'title': isShrouk ? "وقت الشروق" : "وقت الصلاة",
            'body': isShrouk
                ? "حان الآن موعد الشروق"
                : "حان الآن موعد أذان ${AppConsts.prayerNames[i]}",
            'dateTime': prayerDateTime,
            'sound': "res_azan",
          });

          // Add reminder notification to queue if set
          if (reminderTimes[i].isNotEmpty) {
            final reminderDuration =
                reminderDurations[reminderTimes[i]] ?? const Duration();
            final reminderDateTime = prayerDateTime.subtract(reminderDuration);

            if (reminderDateTime.isBefore(DateTime.now())) {
              continue;
            }

            notifications.add({
              'isReminder': true,
              'id': AndroidAwesomeNotifyService.createUniqueId(
                AppConsts.prayerNames[i],
                reminderDateTime,
              ),
              'title': isShrouk ? "تذكير الشروق" : "تذكير الصلاة",
              'body': isShrouk
                  ? "تبقى ${reminderTimes[i]} على الشروق"
                  : "تبقى ${reminderTimes[i]} على أذان ${AppConsts.prayerNames[i]}"
                      .replaceAll("قبل ", ""),
              'dateTime': reminderDateTime,
              'sound': "res_reminder",
            });
          }
        }
      }
    }

    // Sort by date (schedule nearest first)
    notifications.sort((a, b) =>
        (a['dateTime'] as DateTime).compareTo(b['dateTime'] as DateTime));

    // Schedule notifications with iOS limit
    for (final notification in notifications) {
      // Check iOS notification limit
      if (Platform.isIOS &&
          scheduledNotificationsCount >= maxIOSNotifications) {
        Log.w(
            'iOS notification limit reached ($maxIOSNotifications). Skipping remaining notifications.');
        break;
      }

      if (Platform.isIOS) {
        await IOSAwesomeNotifyService.createScheduleNotification(
          isReminder: notification['isReminder'],
          id: notification['id'],
          title: notification['title'],
          body: notification['body'],
          dateTime: notification['dateTime'],
          sound: notification['sound'],
        );
      } else {
        await AndroidAwesomeNotifyService.createScheduleNotification(
          isReminder: notification['isReminder'],
          id: notification['id'],
          title: notification['title'],
          body: notification['body'],
          dateTime: notification['dateTime'],
          sound: notification['sound'],
        );
      }

      scheduledNotificationsCount++;
    }

    Log.w('Total scheduled notifications: $scheduledNotificationsCount');
    Log.w(
        'Schedule_Notifications for Prayer Times: $notificationStates\nReminder_Times: $reminderTimes');
  }


  List<String> _getPrayerTimes() {
    final calendarController = ref.read(calendarControllerNotifierProvider);
    final currentDayData = calendarController.currentDayCalendar;
    return [
      currentDayData.prayerTimes.fajr,
      currentDayData.prayerTimes.sunrise,
      currentDayData.prayerTimes.dhuhr,
      currentDayData.prayerTimes.asr,
      currentDayData.prayerTimes.maghrib,
      currentDayData.prayerTimes.isha,
    ];
  }

  Future<void> cancelAllScheduledNotifications() async {
    await AndroidAwesomeNotifyService.cancelAllScheduledNotifications();
  }
}
