import 'dart:io';
import 'package:background_fetch/background_fetch.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quran_broadcast_app/src/app.dart';
import 'package:quran_broadcast_app/src/core/shared/services/home_widgets_service/background.service.dart';
import 'package:quran_broadcast_app/src/core/shared/services/home_widgets_service/home_widget.service.dart';
import 'package:quran_broadcast_app/src/core/shared/utils/inialize.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await initialize();

  await HomeWidgetService.initialize();

  // Register the background fetch task for Android
  if (Platform.isAndroid) {
    BackgroundFetch.registerHeadlessTask(backgroundFetchHeadlessTask);
  }

  runApp(
    const ProviderScope(
      child: BaseApp(),
    ),
  );
}
